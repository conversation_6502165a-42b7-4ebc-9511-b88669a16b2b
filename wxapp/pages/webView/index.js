Page({

  /**
   * 页面的初始数据
   */
  data: {
    url: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 对传入的URL进行解码处理
    const decodedUrl = options.url ? decodeURIComponent(options.url) : ''
    this.setData({
      url: decodedUrl
    })

    // 如果传入了标题参数，设置页面标题
    if (options.title) {
      wx.setNavigationBarTitle({
        title: decodeURIComponent(options.title)
      })
    }
  },

  /**
   * 接收H5页面发送的消息
   */
  onWebViewMessage(e) {
    console.log('收到H5页面消息:', e.detail.data)
    const { data } = e.detail
    if (data && data.length > 0) {
      const message = data[data.length - 1] // 获取最新消息

      // 处理设置标题的消息
      if (message.type === 'setTitle' && message.title) {
        wx.setNavigationBarTitle({
          title: message.title
        })
      }

      // 处理页面准备就绪的消息
      if (message.type === 'pageReady') {
        console.log('H5页面加载完成:', message.url)
      }
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
